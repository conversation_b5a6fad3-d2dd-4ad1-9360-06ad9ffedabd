package com.tqhit.battery.one.fragment.main.others

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.databinding.FragmentOthersBinding
import com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.fragment.main.HealthFragment
import com.tqhit.battery.one.fragment.main.SettingsFragment
import com.tqhit.battery.one.fragment.main.others.adapter.OthersAdapter
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData.Companion.CHARGE_DISCHARGE_ITEM_ID
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData.Companion.HEALTH_ITEM_ID
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData.Companion.SETTINGS_ITEM_ID
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Others fragment implementing card-based navigation for Charge/Discharge, Battery Health, and Settings.
 *
 * Features:
 * - Dynamic charge/discharge navigation based on real-time battery status
 * - CoreBatteryStatsService integration for battery state monitoring
 * - Anti-theft toggle functionality with password management
 * - MVI architecture pattern with proper state management
 * - Comprehensive logging for debugging and ADB testing
 *
 * Following the established stats module architecture pattern.
 */
@AndroidEntryPoint
class OthersFragment : AdLibBaseFragment<FragmentOthersBinding>() {

    override val binding by lazy { FragmentOthersBinding.inflate(layoutInflater) }

    // Modern architecture dependencies
    @Inject lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider
    @Inject lateinit var dynamicNavigationManager: DynamicNavigationManager
    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager

    private val appViewModel: AppViewModel by viewModels()
    private lateinit var othersAdapter: OthersAdapter
    private var isDeviceCharging: Boolean = false

    companion object {
        private const val TAG = "OthersFragment"
    }

    init {
        Log.d(TAG, "OTHERS_FRAGMENT: Fragment instance created")
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "OTHERS_FRAGMENT: onViewCreated - initializing Others fragment")
        Log.d(TAG, "OTHERS_LIFECYCLE: Fragment view created, starting battery status observation")
        observeBatteryStatus()
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "OTHERS_LIFECYCLE: Fragment resumed, refreshing UI state")
        // Refresh anti-theft toggle state in case it was changed in settings
        try {
            binding.switchEnableAntiThief.isChecked = appViewModel.isAntiThiefEnabled()
            Log.d(TAG, "OTHERS_ANTITHEFT: Refreshed toggle state on resume - ${binding.switchEnableAntiThief.isChecked}")
        } catch (e: Exception) {
            Log.w(TAG, "OTHERS_LIFECYCLE: Binding not available during resume", e)
        }
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "OTHERS_LIFECYCLE: Fragment paused")
    }

    override fun onDestroyView() {
        Log.d(TAG, "OTHERS_LIFECYCLE: Fragment view being destroyed")
        super.onDestroyView()
    }

    override fun setupData() {
        super.setupData()
        Log.d(TAG, "OTHERS_FRAGMENT: setupData - configuring RecyclerView and adapter")

        setupRecyclerView()
        updateAdapterItems()
    }

    override fun setupListener() {
        super.setupListener()
        Log.d(TAG, "OTHERS_FRAGMENT: setupListener - configuring anti-theft toggle")

        setupAntiTheftToggle()
    }

    /**
     * Sets up the RecyclerView with the OthersAdapter.
     * Configures linear layout manager and item click handling.
     */
    private fun setupRecyclerView() {
        Log.d(TAG, "OTHERS_RECYCLERVIEW: Setting up RecyclerView with LinearLayoutManager")

        othersAdapter = OthersAdapter { item ->
            handleItemClick(item)
        }

        binding.othersRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = othersAdapter
        }

        Log.d(TAG, "OTHERS_RECYCLERVIEW: RecyclerView setup complete")
    }

    /**
     * Updates the adapter items based on current battery charging state.
     * Creates appropriate card data for Charge/Discharge, Health, and Settings.
     */
    private fun updateAdapterItems() {
        Log.d(TAG, "OTHERS_ITEMS: Updating adapter items - charging state: $isDeviceCharging")

        val items = listOf(
            createChargeDischargeItem(),
            createHealthItem(),
            createSettingsItem()
        )

        othersAdapter.submitList(items)
        Log.d(TAG, "OTHERS_ITEMS: Submitted ${items.size} items to adapter")
    }

    /**
     * Creates the charge/discharge item based on current battery status.
     * Dynamically updates title, description, and icon based on charging state.
     */
    private fun createChargeDischargeItem(): OthersItemData {
        return if (isDeviceCharging) {
            Log.d(TAG, "OTHERS_ITEMS: Creating discharge item (device is charging)")
            OthersItemData(
                id = CHARGE_DISCHARGE_ITEM_ID,
                title = getString(R.string.discharge),
                description = "View discharge statistics and power consumption details",
                iconResId = R.drawable.ic_discharge_icon
            )
        } else {
            Log.d(TAG, "OTHERS_ITEMS: Creating charge item (device not charging)")
            OthersItemData(
                id = CHARGE_DISCHARGE_ITEM_ID,
                title = getString(R.string.charge),
                description = "Activate charging mode to simulate or enable device charging",
                iconResId = R.drawable.ic_charge_icon
            )
        }
    }

    /**
     * Creates the health item for battery health navigation.
     */
    private fun createHealthItem(): OthersItemData {
        Log.d(TAG, "OTHERS_ITEMS: Creating health item")
        return OthersItemData(
            id = HEALTH_ITEM_ID,
            title = getString(R.string.health),
            description = "View battery health stats including capacity, voltage, and temperature",
            iconResId = R.drawable.ic_health_icon
        )
    }

    /**
     * Creates the settings item for app settings navigation.
     */
    private fun createSettingsItem(): OthersItemData {
        Log.d(TAG, "OTHERS_ITEMS: Creating settings item")
        return OthersItemData(
            id = SETTINGS_ITEM_ID,
            title = getString(R.string.settings),
            description = "Customize app preferences and behavior to fit your needs",
            iconResId = R.drawable.ic_settings_icon
        )
    }

    /**
     * Handles click events for card items.
     * Routes to appropriate fragments based on item ID and current battery state.
     */
    private fun handleItemClick(item: OthersItemData) {
        Log.d(TAG, "OTHERS_NAVIGATION: Item clicked - ${item.id}")

        when (item.id) {
            CHARGE_DISCHARGE_ITEM_ID -> {
                handleChargeDischargeNavigation()
            }
            HEALTH_ITEM_ID -> {
                Log.d(TAG, "OTHERS_NAVIGATION: Navigating to HealthFragment")
                navigateToFragment(HealthFragment())
            }
            SETTINGS_ITEM_ID -> {
                Log.d(TAG, "OTHERS_NAVIGATION: Navigating to SettingsFragment")
                navigateToFragment(SettingsFragment())
            }
            else -> {
                Log.w(TAG, "OTHERS_NAVIGATION: Unknown item clicked - ${item.id}")
            }
        }
    }

    /**
     * Handles charge/discharge navigation based on current battery state.
     * Uses DynamicNavigationManager for proper fragment routing.
     */
    private fun handleChargeDischargeNavigation() {
        Log.d(TAG, "OTHERS_NAVIGATION: Handling charge/discharge navigation - charging: $isDeviceCharging")

        var navigationSuccess = false

        // Try DynamicNavigationManager first
        if (dynamicNavigationManager.isInitialized()) {
            val targetFragmentId = if (isDeviceCharging) {
                Log.d(TAG, "OTHERS_NAVIGATION: Device charging - navigating to discharge fragment")
                R.id.dischargeFragment
            } else {
                Log.d(TAG, "OTHERS_NAVIGATION: Device not charging - navigating to charge fragment")
                R.id.chargeFragment
            }

            navigationSuccess = dynamicNavigationManager.handleUserNavigation(targetFragmentId)
            Log.d(TAG, "OTHERS_NAVIGATION: DynamicNavigationManager result: $navigationSuccess")

            if (!navigationSuccess) {
                Log.w(TAG, "OTHERS_NAVIGATION: DynamicNavigationManager failed, using fallback navigation")
                performFallbackNavigation(targetFragmentId)
            }
        } else {
            Log.w(TAG, "OTHERS_NAVIGATION: DynamicNavigationManager not initialized, using fallback")
            val targetFragmentId = if (isDeviceCharging) R.id.dischargeFragment else R.id.chargeFragment
            performFallbackNavigation(targetFragmentId)
        }
    }

    /**
     * Performs fallback navigation when DynamicNavigationManager fails.
     * Uses direct fragment manager navigation.
     */
    private fun performFallbackNavigation(fragmentId: Int) {
        Log.d(TAG, "OTHERS_NAVIGATION: Performing fallback navigation to fragment ID: $fragmentId")

        try {
            val fragment = when (fragmentId) {
                R.id.chargeFragment -> {
                    Log.d(TAG, "OTHERS_NAVIGATION: Creating StatsChargeFragment")
                    com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment()
                }
                R.id.dischargeFragment -> {
                    Log.d(TAG, "OTHERS_NAVIGATION: Creating DischargeFragment")
                    com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment()
                }
                else -> {
                    Log.e(TAG, "OTHERS_NAVIGATION: Unknown fragment ID: $fragmentId")
                    return
                }
            }

            // Use activity's fragment manager for proper navigation
            requireActivity().supportFragmentManager
                .beginTransaction()
                .replace(R.id.nav_host_fragment, fragment)
                .addToBackStack(null)
                .commit()

            Log.d(TAG, "OTHERS_NAVIGATION: Fallback navigation completed successfully")

        } catch (e: Exception) {
            Log.e(TAG, "OTHERS_NAVIGATION: Error in fallback navigation", e)
        }
    }

    /**
     * Navigates to the specified fragment using fragment manager.
     * Fallback method for direct fragment navigation.
     */
    private fun navigateToFragment(fragment: androidx.fragment.app.Fragment) {
        Log.d(TAG, "OTHERS_NAVIGATION: Direct fragment navigation to ${fragment.javaClass.simpleName}")

        parentFragmentManager.beginTransaction()
            .replace(R.id.nav_host_fragment, fragment)
            .addToBackStack(null)
            .commit()
    }

    /**
     * Observes battery status changes from CoreBatteryStatsProvider.
     * Updates UI when charging state changes.
     */
    private fun observeBatteryStatus() {
        Log.d(TAG, "OTHERS_BATTERY: Starting battery status observation")

        lifecycleScope.launch {
            try {
                Log.d(TAG, "OTHERS_BATTERY: Collecting from coreBatteryStatusFlow")
                coreBatteryStatsProvider.coreBatteryStatusFlow
                    .filterNotNull()
                    .collect { status ->
                        Log.d(TAG, "OTHERS_BATTERY: Received battery status - charging: ${status.isCharging}, percentage: ${status.percentage}")
                        val wasCharging = isDeviceCharging
                        isDeviceCharging = status.isCharging

                        if (wasCharging != isDeviceCharging) {
                            Log.d(TAG, "OTHERS_BATTERY: Charging state changed from $wasCharging to $isDeviceCharging")
                            updateAdapterItems()
                        } else {
                            Log.v(TAG, "OTHERS_BATTERY: Charging state unchanged: $isDeviceCharging")
                        }
                    }
            } catch (e: Exception) {
                Log.e(TAG, "OTHERS_BATTERY: Error observing battery status", e)
                // Fallback to default state and try to get current status
                try {
                    val currentStatus = coreBatteryStatsProvider.getCurrentStatus()
                    if (currentStatus != null) {
                        Log.d(TAG, "OTHERS_BATTERY: Using current status as fallback - charging: ${currentStatus.isCharging}")
                        isDeviceCharging = currentStatus.isCharging
                    } else {
                        Log.w(TAG, "OTHERS_BATTERY: No current status available, defaulting to not charging")
                        isDeviceCharging = false
                    }
                } catch (fallbackException: Exception) {
                    Log.e(TAG, "OTHERS_BATTERY: Error getting current status", fallbackException)
                    isDeviceCharging = false
                }
                updateAdapterItems()
            }
        }
    }

    /**
     * Sets up the anti-theft toggle switch functionality.
     * Handles toggle state changes and password management.
     */
    private fun setupAntiTheftToggle() {
        Log.d(TAG, "OTHERS_ANTITHEFT: Setting up anti-theft toggle")

        // Initialize toggle state
        binding.switchEnableAntiThief.isChecked = appViewModel.isAntiThiefEnabled()
        Log.d(TAG, "OTHERS_ANTITHEFT: Initial toggle state - ${binding.switchEnableAntiThief.isChecked}")

        // Set toggle change listener
        binding.switchEnableAntiThief.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "OTHERS_ANTITHEFT: Toggle changed - $isChecked")
            handleAntiTheftToggle(isChecked)
        }

        // Set info icon click listener
        binding.antiThiefInfo.setOnClickListener {
            Log.d(TAG, "OTHERS_ANTITHEFT: Info icon clicked")
            showAntiTheftInfoDialog()
        }
    }

    /**
     * Handles anti-theft toggle state changes.
     * Shows password dialog if needed or updates preference directly.
     */
    private fun handleAntiTheftToggle(isEnabled: Boolean) {
        Log.d(TAG, "OTHERS_ANTITHEFT: Handling toggle change - enabled: $isEnabled")

        if (isEnabled && !appViewModel.isAntiThiefPasswordSet()) {
            Log.d(TAG, "OTHERS_ANTITHEFT: Enabling anti-theft but no password set - showing password dialog")
            showPasswordSetupDialog()
        } else {
            Log.d(TAG, "OTHERS_ANTITHEFT: Updating anti-theft preference - $isEnabled")
            appViewModel.setAntiThiefEnabled(isEnabled)
        }
    }

    /**
     * Shows the password setup dialog for anti-theft feature.
     */
    private fun showPasswordSetupDialog() {
        Log.d(TAG, "OTHERS_ANTITHEFT: Showing password setup dialog")

        try {
            val dialog = SetupPasswordDialog(
                context = requireContext(),
                onConfirm = { password ->
                    Log.d(TAG, "OTHERS_ANTITHEFT: Password set successfully")
                    appViewModel.setAntiThiefPassword(password)
                    appViewModel.setAntiThiefEnabled(true)
                    // Refresh toggle state to reflect the change
                    binding.switchEnableAntiThief.isChecked = true
                    Log.d(TAG, "OTHERS_ANTITHEFT: Toggle state updated after password setup")
                },
                onCancel = {
                    Log.d(TAG, "OTHERS_ANTITHEFT: Password setup cancelled")
                    // Reset toggle state if cancelled
                    binding.switchEnableAntiThief.isChecked = false
                }
            )
            dialog.show()
        } catch (e: Exception) {
            Log.e(TAG, "OTHERS_ANTITHEFT: Error showing password setup dialog", e)
            // Reset toggle state if dialog fails
            binding.switchEnableAntiThief.isChecked = false
        }
    }

    /**
     * Shows information dialog about the anti-theft feature.
     */
    private fun showAntiTheftInfoDialog() {
        Log.d(TAG, "OTHERS_ANTITHEFT: Showing anti-theft info dialog")

        val dialog = NotificationDialog(
            requireContext(),
            getString(R.string.anti_thief),
            getString(R.string.anti_thief_info)
        )
        dialog.show()

        Log.d(TAG, "OTHERS_ANTITHEFT: Anti-theft info dialog displayed")
    }
}